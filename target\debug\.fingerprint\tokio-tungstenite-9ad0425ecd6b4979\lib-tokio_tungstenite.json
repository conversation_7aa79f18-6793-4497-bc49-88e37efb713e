{"rustc": 1842507548689473721, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"url\", \"webpki-roots\"]", "target": 10194999948271016277, "profile": 15657897354478470176, "path": 3942086151908485922, "deps": [[4254813506600451364, "tungstenite", false, 2544535889939327965], [5986029879202738730, "log", false, 7463655833432837285], [10629569228670356391, "futures_util", false, 14468474742519639704], [12186126227181294540, "tokio_native_tls", false, 1035164197529610303], [12944427623413450645, "tokio", false, 9011514202329512846], [16785601910559813697, "native_tls_crate", false, 756758295800197205]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-tungstenite-9ad0425ecd6b4979\\dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}