{"rustc": 1842507548689473721, "features": "[\"client\", \"default\", \"http1\", \"http2\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 10765686629543842738, "path": 12930008050848458190, "deps": [[1569313478171189446, "want", false, 504613664166647852], [1811549171721445101, "futures_channel", false, 16376233463899576133], [1906322745568073236, "pin_project_lite", false, 17143027058502063303], [3666196340704888985, "smallvec", false, 7473144789055188591], [6163892036024256188, "httparse", false, 2780161269485313973], [7695812897323945497, "itoa", false, 1783505662873882551], [9010263965687315507, "http", false, 3736580640712029992], [10629569228670356391, "futures_util", false, 14468474742519639704], [12944427623413450645, "tokio", false, 9011514202329512846], [14084095096285906100, "http_body", false, 1581608618866217959], [14359893265615549706, "h2", false, 15399357036328576602], [16066129441945555748, "bytes", false, 7899407690149537674]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-52e832eb8aa70535\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}